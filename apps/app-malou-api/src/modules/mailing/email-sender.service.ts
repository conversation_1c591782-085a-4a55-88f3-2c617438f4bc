import AWS, { AWSError } from 'aws-sdk';
import { SendCustomVerificationEmailRequest, SendCustomVerificationEmailResponse, SendRawEmailRequest } from 'aws-sdk/clients/ses';
import { PromiseResult } from 'aws-sdk/lib/request';
import { createTransport, SentMessageInfo, Transporter } from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import { singleton } from 'tsyringe';

import { APP_DEFAULT_LOCALE, Locale } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { Translation } from ':services/translation.service';

// The source code actually accept a ses object but the Mail.Options interface doesn't get it
export interface MailOptions extends Mail.Options {
    ses?: Partial<SendRawEmailRequest>;
    lang?: Locale;
    fromEmail?: string;
}

@singleton()
export class EmailSenderService {
    ses: AWS.SES;

    transporter: Transporter<SentMessageInfo>;

    constructor(private readonly _translator: Translation) {
        this.ses = new AWS.SES({
            region: Config.services.aws.region,
            accessKeyId: Config.services.aws.key,
            secretAccessKey: Config.services.aws.secret,
        });

        this.transporter = createTransport({
            host: 'localhost',
            port: 1025,
        });
        // this.transporter = createTransport({
        //     SES: this.ses,
        // });
    }

    async sendEmail(options: MailOptions): Promise<SentMessageInfo> {
        const finalOptions = { ...options };
        const bourdoncleMailingList = [
            // TODO : remove when not needed anymore. This is a temporary fix for bourdoncle who seem not to receive some of the emails. We put Clemence as CC for all the emails sent to bourdoncle emails.
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        const from =
            options.from ??
            `=?UTF-8?B?${Buffer.from(
                this._translator
                    .fromLang({ lang: options.lang ?? APP_DEFAULT_LOCALE })
                    .common.malouTeam({ env: process.env.NODE_ENV === 'production' ? '' : process.env.NODE_ENV })
            ).toString('base64')}?= <${options?.fromEmail ?? '<EMAIL>'}>`;
        if (typeof options.to === 'string' && bourdoncleMailingList.includes(options.to.toLowerCase())) {
            logger.info('Sending email to bourdoncle mailing list, bcc Clémence', options);
            finalOptions.bcc = '<EMAIL>';
        }
        return this.transporter.sendMail({
            ...finalOptions,
            from,
        });
    }

    async sendSESIdentityVerificationEmail(
        options: SendCustomVerificationEmailRequest
    ): Promise<PromiseResult<SendCustomVerificationEmailResponse, AWSError>> {
        return this.ses.sendCustomVerificationEmail(options).promise();
    }
}
