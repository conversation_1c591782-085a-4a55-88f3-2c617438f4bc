import 'reflect-metadata';

import ':env';

import prompts from 'prompts';

import { CredentialModel } from '@malou-io/package-models';

import { DOORDASH_SUPER_CREDENTIAL_KEY } from ':modules/credentials/platforms/doordash/doordash.repository';
import ':plugins/db';

async function getCookie(): Promise<string> {
    const response = await prompts({
        type: 'text',
        name: 'value',
        message: `DoorDash cookie`,
        hint: 'https://www.notion.so/welcomehomemalou/Token-DoorDash-22ac978b4cbe80b0bcecef0c756c661c',
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    return response.value;
}

const main = async () => {
    const cookie = await getCookie();
    await CredentialModel.updateOne({ key: DOORDASH_SUPER_CREDENTIAL_KEY }, { cookie });
};

main()
    .then(() => {
        console.log('Done !');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
