import 'reflect-metadata';

import ':env';

import prompts from 'prompts';

import { CredentialModel } from '@malou-io/package-models';

import ':plugins/db';

async function getCookie(): Promise<string> {
    const response = await prompts({
        type: 'text',
        name: 'value',
        message: `Uber Eats cookie`,
        hint: 'https://www.notion.so/welcomehomemalou/Token-Ubereats-890ee5d24d4f4311bda4f8e0dd1ccac9',
        initial: false,
    });

    if (!response.value) {
        console.log('exit...');
        process.exit(0);
    }

    return response.value;
}

const main = async () => {
    const cookie = await getCookie();
    await CredentialModel.updateOne({ key: 'ubereats-malou' }, { cookie });
};

main()
    .then(() => {
        console.log('Done !');
        process.exit(0);
    })
    .catch((error) => {
        console.warn('err >>', error);
        process.exit(1);
    });
