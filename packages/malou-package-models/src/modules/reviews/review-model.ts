import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { ITranslations } from '@malou-io/package-utils';

import { OverwriteOrAssign } from ':core/index';
import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { RemoveNullOrUndefined } from ':core/mongoose-json-schema/type-utils';
import { IReviewAnalysis } from ':modules/review-analyses/review-analysis-model';
import { ISegmentAnalysis } from ':modules/segment-analyses/segment-analyses-model';

import { reviewJSONSchema } from './review-schema';

const reviewSchema = createMongooseSchemaFromJSONSchema(reviewJSONSchema);

reviewSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

reviewSchema.virtual('platform', {
    ref: 'Platform',
    localField: 'platformId',
    foreignField: '_id',
    justOne: true,
});

reviewSchema.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
});

reviewSchema.virtual('semanticAnalysis', {
    // not tested yet ! doc: https://github.com/Automattic/mongoose/issues/6608#issuecomment-723662319
    ref: 'ReviewAnalysis',
    localField: 'socialId',
    foreignField: 'reviewSocialId',
    match: (doc: IReview) => ({ platformKey: doc.key }),
    justOne: true,
});

reviewSchema.path('aiRelevantBricks').schema?.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
    options: { lean: true },
});

reviewSchema.index({ socialId: 1, platformId: 1 }, { unique: true });
reviewSchema.index({ publicBusinessId: 1 }, { unique: true, sparse: true });
reviewSchema.index({ restaurantId: 1, key: 1, socialCreatedAt: 1 });
reviewSchema.index({ socialCreatedAt: 1 });
reviewSchema.index({ 'comments._id': 1 });
reviewSchema.index({ platformId: 1, restaurantId: 1 });
reviewSchema.index({ restaurantId: 1, socialUpdatedAt: 1, socialCreatedAt: 1 });
reviewSchema.index({ key: 1, restaurantId: 1, rating: 1, archived: 1, comments: 1, socialUpdatedAt: 1, socialCreatedAt: 1 });
reviewSchema.index({ restaurantId: 1, platformId: 1, socialCreatedAt: 1, socialUpdatedAt: 1 });

// For pages that list reviews with endpoints like GET /reviews/v2
// See getRestaurantReviewsPaginatedV2
reviewSchema.index({ restaurantId: 1, socialSortDate: 1 });

/** From DB */

reviewSchema.index({ 'comments.posted': 1, 'comments.socialUpdatedAt': 1 });

/** */

reviewSchema.path('comments').schema?.index({
    posted: 1,
    socialUpdatedAt: 1,
});

export type IReview = FromSchema<
    typeof reviewJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IReviewComment = IReview['comments'][0];
export type IReviewSocialAttachment = RemoveNullOrUndefined<IReview['socialAttachments']>[0];
export type IReviewWithSemanticAnalysis = OverwriteOrAssign<
    IReview,
    { semanticAnalysis: IReviewAnalysis; semanticAnalysisSegments: ISegmentAnalysis[] } // TODO: Remove semanticAnalysis when feature toggle 'release-new-semantic-analysis' is removed
>;
export type IReviewWithSemanticAnalysisAndTranslations = OverwriteOrAssign<IReviewWithSemanticAnalysis, { translations: ITranslations }>;

export const ReviewModel = mongoose.model<IReview>(reviewJSONSchema.title, reviewSchema);

export type IReviewWithTranslations = OverwriteOrAssign<IReview, { translations?: ITranslations }>;
