import { FromSchema } from 'json-schema-to-ts';
import { MongoError } from 'mongodb';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { campaignJSONSchema } from './campaign-schema';

const campaignSchema = createMongooseSchemaFromJSONSchema(campaignJSONSchema);

campaignSchema.index({ restaurantId: 1, createdAt: 1 });

campaignSchema.path('contactInteractions').schema?.virtual('client', {
    ref: 'Client',
    localField: 'clientId',
    foreignField: '_id',
    justOne: true,
});

campaignSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

campaignSchema.index({ name: 1, restaurantId: 1 }, { unique: true });

// Handle 11000 duplicate key
campaignSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if (error instanceof MongoError && error.code === 11000) {
        return next({ duplicateRecordError: true } as any);
    }
    return next(error);
});

export type ICampaign = FromSchema<
    typeof campaignJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const CampaignModel = mongoose.model<ICampaign>(campaignJSONSchema.title, campaignSchema);
