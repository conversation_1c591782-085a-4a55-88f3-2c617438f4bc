import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { PostModel } from ':modules/posts/post-model';

import { feedbackJSONSchema } from './feedback-schema';

const feedbackSchema = createMongooseSchemaFromJSONSchema(feedbackJSONSchema);

feedbackSchema.index({ restaurantId: 1 });
feedbackSchema.index({ 'participants.participant._id': 1 });
feedbackSchema.index({ 'feedbackMessages.author._id': 1 });

feedbackSchema.path('participants').schema?.path('participant').schema?.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true,
});

feedbackSchema.pre('deleteOne', async function (next) {
    try {
        const feedbackId = this.getQuery()._id;
        await PostModel.findOneAndUpdate({ feedbackId }, { feedbackId: null }, { upsert: true }).exec();
    } catch (error) {
        console.warn('feedback deleteOne error :>>', error);
        next(null);
    }
});

export type IFeedback = FromSchema<
    typeof feedbackJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IFeedbackMessage = IFeedback['feedbackMessages'][0];

export const FeedbackModel = mongoose.model<IFeedback>(feedbackJSONSchema.title, feedbackSchema);
