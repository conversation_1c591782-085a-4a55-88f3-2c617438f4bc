import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { restaurantAiSettingsJSONSchema } from './restaurant-ai-settings-schema';

const restaurantAiSettingsSchema = createMongooseSchemaFromJSONSchema(restaurantAiSettingsJSONSchema);

restaurantAiSettingsSchema.path('reviewSettings').schema?.virtual('signatureTranslations', {
    ref: 'Translations',
    localField: 'signatureTranslationIds',
    foreignField: '_id',
    justOne: false,
});

restaurantAiSettingsSchema.path('reviewSettings').schema?.virtual('catchphraseTranslation', {
    ref: 'Translations',
    localField: 'catchphraseTranslationId',
    foreignField: '_id',
    justOne: true,
});

restaurantAiSettingsSchema.index({ restaurantId: 1 }, { unique: true });

export type IRestaurantAiSettings = FromSchema<
    typeof restaurantAiSettingsJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const RestaurantAiSettingsModel = mongoose.model<IRestaurantAiSettings>(
    restaurantAiSettingsJSONSchema.title,
    restaurantAiSettingsSchema
);
