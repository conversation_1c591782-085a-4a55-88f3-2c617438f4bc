import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { wheelOfFortuneJSONSchema } from './wheel-of-fortune-schema';

const wheelOfFortuneSchema = createMongooseSchemaFromJSONSchema(wheelOfFortuneJSONSchema);

wheelOfFortuneSchema.virtual('gifts', {
    ref: 'Gift',
    localField: 'giftIds',
    foreignField: '_id',
    justOne: false,
});

wheelOfFortuneSchema.virtual('totems', {
    ref: 'Nfcs',
    localField: 'totemIds',
    foreignField: '_id',
    justOne: false,
});

wheelOfFortuneSchema.path('parameters').schema?.virtual('media', {
    ref: 'Media',
    localField: 'mediaId',
    foreignField: '_id',
    justOne: true,
});

export type IWheelOfFortune = FromSchema<
    typeof wheelOfFortuneJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const WheelOfFortuneModel = mongoose.model<IWheelOfFortune>(wheelOfFortuneJSONSchema.title, wheelOfFortuneSchema, 'wheelsoffortune');
